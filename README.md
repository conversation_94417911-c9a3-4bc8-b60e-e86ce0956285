# 淘宝产品检查插件

这是一个Chrome浏览器扩展插件，用于批量检查淘宝产品的佣金信息和规则符合性。

## 功能特点

1. **批量产品检查**：支持一次性检查最多200个产品ID
2. **双重验证**：每个产品会检查两次，确保数据准确性
3. **智能分类**：自动将产品分为三类：
   - 不符合规则（有违规标记）
   - 无佣金（符合规则但无佣金或佣金为0）
   - 有佣金（符合规则且有佣金）
4. **Excel导出**：支持分类导出到Excel文件
5. **本地存储**：数据保存在浏览器本地，支持清空功能

## 使用方法

1. 在任意淘宝页面打开插件界面
2. 在文本框中输入产品ID，一行一个
3. 点击"检测产品"按钮开始检查
4. 检查完成后可以导出Excel文件
5. 支持清空输入框和清空所有数据

## 检查的数据字段

- 商品ID (itemId)
- 商品名称 (itemName)  
- 商品价格 (itemPrice)
- 佣金比例 (tcpCommission)
- 佣金类型 (tcpCommissionType)
- 热门标签 (hotItemTag)
- 违规原因 (conditionsTitle)
- 检查时间

## 安装方法

1. 下载插件文件
2. 打开Chrome浏览器，进入扩展程序管理页面
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

## 注意事项

- 需要在淘宝网站上使用
- 建议分批检查，避免请求过于频繁
- 数据保存在本地浏览器中，清除浏览器数据会丢失
