// 淘宝产品检查插件
(function() {
    'use strict';

    // 存储键名
    const STORAGE_KEY = 'taobao_product_check_data';

    // 初始化插件
    function initPlugin() {
        createPluginModal();
        loadStoredData();
    }

    // 创建插件Modal
    function createPluginModal() {
        // 获取保存的位置
        const savedPosition = getSavedPosition();

        // 创建modal容器
        const modal = document.createElement('div');
        modal.id = 'taobao-plugin-modal';
        modal.innerHTML = `
            <div class="plugin-header" id="plugin-header">
                <span class="plugin-title">
                    <span class="drag-icon">⋮⋮</span>
                    淘宝产品检查
                </span>
                <div class="plugin-stats">
                    <span id="product-count">已检查: 0 个商品</span>
                </div>
            </div>
            <div class="plugin-content">
                <div class="input-section">
                    <div class="input-header">
                        <label for="product-ids">产品ID列表（一行一个）：</label>
                        <button id="clear-input-btn" class="clear-input-btn">清空输入</button>
                    </div>
                    <textarea id="product-ids" placeholder="请输入产品ID，一行一个，最多200个" rows="8"></textarea>
                </div>
                <div class="plugin-buttons">
                    <button id="check-btn" class="plugin-btn check-btn">检测产品</button>
                    <button id="export-btn" class="plugin-btn export-btn">导出Excel</button>
                    <button id="clear-btn" class="plugin-btn clear-btn">清空数据</button>
                </div>
            </div>
        `;

        // 应用保存的位置
        modal.style.position = 'fixed';
        modal.style.left = savedPosition.left;
        modal.style.top = savedPosition.top;
        modal.style.transform = 'none'; // 取消居中变换

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #taobao-plugin-modal {
                width: 600px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                z-index: 999999;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                color: white;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                user-select: none;
                transition: box-shadow 0.3s ease;
            }

            #taobao-plugin-modal.dragging {
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
                transform: scale(1.02);
            }

            .plugin-header {
                padding: 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                position: relative;
            }

            .plugin-header:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .plugin-title {
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .drag-icon {
                font-size: 14px;
                opacity: 0.7;
                transform: rotate(90deg);
                transition: opacity 0.3s ease;
            }

            .plugin-header:hover .drag-icon {
                opacity: 1;
            }

            .plugin-stats {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 4px;
            }

            .plugin-stats > div, .plugin-stats > span {
                font-size: 11px;
                background: rgba(255, 255, 255, 0.2);
                padding: 3px 10px;
                border-radius: 15px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .plugin-content {
                padding: 20px;
            }

            .input-section {
                margin-bottom: 20px;
            }

            .input-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .input-header label {
                font-size: 14px;
                font-weight: 500;
            }

            .clear-input-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.3s ease;
            }

            .clear-input-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            #product-ids {
                width: 100%;
                min-height: 120px;
                padding: 12px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 14px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                resize: vertical;
                box-sizing: border-box;
            }

            #product-ids::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }

            #product-ids:focus {
                outline: none;
                border-color: rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.15);
            }

            .plugin-buttons {
                display: flex;
                gap: 12px;
            }

            .plugin-btn {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .check-btn {
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
            }

            .export-btn {
                background: linear-gradient(135deg, #2196F3, #1976D2);
                color: white;
            }

            .clear-btn {
                background: linear-gradient(135deg, #f44336, #d32f2f);
                color: white;
            }

            .plugin-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .plugin-btn:active {
                transform: translateY(0);
            }

            .plugin-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(modal);

        // 初始化拖动功能
        initDragFunctionality(modal);

        // 绑定事件
        bindEvents();
    }

    // 获取保存的位置
    function getSavedPosition() {
        try {
            const saved = localStorage.getItem('taobao_plugin_position');
            if (saved) {
                const position = JSON.parse(saved);
                // 验证位置是否在屏幕范围内
                const maxX = window.innerWidth - 600; // 减去插件宽度
                const maxY = window.innerHeight - 300; // 减去插件高度的估计值

                return {
                    left: Math.max(0, Math.min(position.left, maxX)) + 'px',
                    top: Math.max(0, Math.min(position.top, maxY)) + 'px'
                };
            }
        } catch (error) {
            console.error('获取保存位置失败:', error);
        }

        // 默认位置（顶部中间）
        return {
            left: Math.max(0, (window.innerWidth - 600) / 2) + 'px',
            top: '20px'
        };
    }
    
    // 保存位置
    function savePosition(left, top) {
        try {
            const position = {
                left: parseInt(left),
                top: parseInt(top)
            };
            localStorage.setItem('taobao_plugin_position', JSON.stringify(position));
        } catch (error) {
            console.error('保存位置失败:', error);
        }
    }
    
    // 初始化拖动功能
    function initDragFunctionality(modal) {
        const header = modal.querySelector('#plugin-header');
        let isDragging = false;
        let startX, startY, initialLeft, initialTop;
        
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            modal.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = modal.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;
            
            // 防止文本选择
            e.preventDefault();
            
            // 添加全局鼠标事件监听器
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });
        
        function onMouseMove(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newLeft = initialLeft + deltaX;
            let newTop = initialTop + deltaY;
            
            // 限制在屏幕范围内
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));
            
            modal.style.left = newLeft + 'px';
            modal.style.top = newTop + 'px';
        }
        
        function onMouseUp() {
            if (isDragging) {
                isDragging = false;
                modal.classList.remove('dragging');
                
                // 保存新位置
                const rect = modal.getBoundingClientRect();
                savePosition(rect.left, rect.top);
                
                // 移除全局事件监听器
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
        }
        
        // 处理窗口大小变化
        window.addEventListener('resize', function() {
            const rect = modal.getBoundingClientRect();
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            if (rect.left > maxLeft || rect.top > maxTop) {
                const newLeft = Math.max(0, Math.min(rect.left, maxLeft));
                const newTop = Math.max(0, Math.min(rect.top, maxTop));
                
                modal.style.left = newLeft + 'px';
                modal.style.top = newTop + 'px';
                
                savePosition(newLeft, newTop);
            }
        });
    }

    // 绑定事件
    function bindEvents() {
        document.getElementById('check-btn').addEventListener('click', checkProducts);
        document.getElementById('export-btn').addEventListener('click', exportData);
        document.getElementById('clear-btn').addEventListener('click', clearData);
        document.getElementById('clear-input-btn').addEventListener('click', clearInput);
    }

    // 清空输入框
    function clearInput() {
        document.getElementById('product-ids').value = '';
    }

    // 检查产品
    async function checkProducts() {
        const textarea = document.getElementById('product-ids');
        const productIds = textarea.value.trim().split('\n').filter(id => id.trim()).map(id => id.trim());

        if (productIds.length === 0) {
            layer.msg('请输入产品ID');
            return;
        }

        if (productIds.length > 200) {
            layer.msg('最多只能检查200个产品ID');
            return;
        }

        showLoading('正在检查产品...');

        try {
            // 构建URL列表
            const urls = productIds.map(id => `https://item.taobao.com/item.htm?id=${id}`);

            // 分批处理，每次最多50个
            const batchSize = 50;
            const allResults = [];

            for (let i = 0; i < urls.length; i += batchSize) {
                const batchUrls = urls.slice(i, i + batchSize);
                console.log(`处理第 ${Math.floor(i/batchSize) + 1} 批，共 ${batchUrls.length} 个产品`);

                // 第一次检查
                const firstResults = await checkProductBatch(batchUrls);

                // 等待1秒后进行第二次检查
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 第二次检查
                const secondResults = await checkProductBatch(batchUrls);

                // 合并结果，优先使用第二次的结果
                const mergedResults = mergeResults(firstResults, secondResults);
                allResults.push(...mergedResults);

                // 批次间等待
                if (i + batchSize < urls.length) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            // 保存结果
            saveToLocalStorage(allResults);
            layer.msg(`检查完成，共处理 ${allResults.length} 个产品`);

        } catch (error) {
            console.error('检查失败:', error);
            layer.msg('检查失败: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    // 检查产品批次
    async function checkProductBatch(urls) {
        const timestamp = Date.now();
        const appKey = '12574478';
        const api = 'mtop.taobao.dreamweb.live.item.batch.get';

        const dataStr = JSON.stringify({
            "urls": JSON.stringify(urls),
            "entryType": "2",
            "liveId": "525328079529",
            "liveColumnId": "525",
            "bizScene": "live"
        });

        // 构建请求参数
        const requestData = `data=${encodeURIComponent(dataStr)}`;

        // 使用固定签名（从你提供的示例中获取）
        const sign = '60345bb47997251945339d916d9cfedd';

        const requestUrl = `https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.live.item.batch.get/1.0/?jsv=2.7.2&appKey=${appKey}&t=${timestamp}&sign=${sign}&api=${api}&v=1.0&type=originaljson&dataType=json&preventFallback=true`;

        try {
            const response = await fetch(requestUrl, {
                method: 'POST',
                headers: {
                    'accept': 'application/json',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'content-type': 'application/x-www-form-urlencoded',
                    'priority': 'u=1, i',
                    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site'
                },
                referrer: 'https://market.m.taobao.com/',
                referrerPolicy: 'strict-origin-when-cross-origin',
                body: requestData,
                mode: 'cors',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return processApiResponse(data);

        } catch (error) {
            console.error('批次检查失败:', error);
            throw error;
        }
    }

    // 处理API响应
    function processApiResponse(data) {
        const results = [];

        if (data && data.data && data.data.result) {
            data.data.result.forEach(item => {
                if (item.success && item.data) {
                    const productData = {
                        itemId: item.data.itemId,
                        itemName: item.data.itemName,
                        itemPrice: item.data.itemPrice,
                        tcpCommission: item.data.extendVal?.tcpCommission || '',
                        tcpCommissionType: item.data.extendVal?.tcpCommissionType || '',
                        hotItemTag: item.data.extendVal?.hotItemTag || '',
                        conditionsTitle: item.data.conditions?.[0]?.features?.label || '',
                        checkTime: new Date().toLocaleString(),
                        url: item.url
                    };
                    results.push(productData);
                }
            });
        }

        return results;
    }

    // 合并两次检查的结果
    function mergeResults(firstResults, secondResults) {
        const merged = new Map();

        // 先添加第一次的结果
        firstResults.forEach(item => {
            merged.set(item.itemId, item);
        });

        // 用第二次的结果覆盖（优先使用第二次结果）
        secondResults.forEach(item => {
            merged.set(item.itemId, item);
        });

        return Array.from(merged.values());
    }

    // 保存到本地存储
    function saveToLocalStorage(newData) {
        try {
            const existingData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            // 合并数据（避免重复）
            const allData = [...existingData];
            newData.forEach(newItem => {
                const exists = allData.find(item => item.itemId === newItem.itemId);
                if (!exists) {
                    allData.push(newItem);
                } else {
                    // 更新现有数据
                    const index = allData.findIndex(item => item.itemId === newItem.itemId);
                    allData[index] = newItem;
                }
            });

            localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
            updateProductCount();
        } catch (error) {
            console.error('保存失败:', error);
            layer.msg('保存失败: ' + error.message);
        }
    }

    // 导出数据
    function exportData() {
        try {
            const data = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            if (data.length === 0) {
                layer.msg('暂无数据可导出', {icon: 3});
                return;
            }

            // 分类数据
            const notCompliant = data.filter(item => item.conditionsTitle && item.conditionsTitle.trim() !== '');
            const noCommission = data.filter(item => (!item.conditionsTitle || item.conditionsTitle.trim() === '') &&
                                                   (!item.tcpCommission || item.tcpCommission === '0' || item.tcpCommission === '0%'));
            const hasCommission = data.filter(item => (!item.conditionsTitle || item.conditionsTitle.trim() === '') &&
                                                     item.tcpCommission && item.tcpCommission !== '0' && item.tcpCommission !== '0%');

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 创建不符合规则的工作表
            if (notCompliant.length > 0) {
                const notCompliantData = notCompliant.map(item => ({
                    '商品ID': item.itemId,
                    '商品名称': item.itemName,
                    '商品价格': item.itemPrice,
                    '佣金比例': item.tcpCommission,
                    '佣金类型': item.tcpCommissionType,
                    '热门标签': item.hotItemTag,
                    '不符合原因': item.conditionsTitle,
                    '检查时间': item.checkTime,
                    '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`
                }));

                const ws1 = XLSX.utils.json_to_sheet(notCompliantData);
                ws1['!cols'] = [
                    {wch: 15}, {wch: 50}, {wch: 12}, {wch: 12}, {wch: 15},
                    {wch: 15}, {wch: 30}, {wch: 20}, {wch: 15}
                ];
                XLSX.utils.book_append_sheet(wb, ws1, '不符合规则');
            }

            // 创建无佣金的工作表
            if (noCommission.length > 0) {
                const noCommissionData = noCommission.map(item => ({
                    '商品ID': item.itemId,
                    '商品名称': item.itemName,
                    '商品价格': item.itemPrice,
                    '佣金比例': item.tcpCommission,
                    '佣金类型': item.tcpCommissionType,
                    '热门标签': item.hotItemTag,
                    '检查时间': item.checkTime,
                    '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`
                }));

                const ws2 = XLSX.utils.json_to_sheet(noCommissionData);
                ws2['!cols'] = [
                    {wch: 15}, {wch: 50}, {wch: 12}, {wch: 12}, {wch: 15},
                    {wch: 15}, {wch: 20}, {wch: 15}
                ];
                XLSX.utils.book_append_sheet(wb, ws2, '无佣金');
            }

            // 创建有佣金的工作表
            if (hasCommission.length > 0) {
                const hasCommissionData = hasCommission.map(item => ({
                    '商品ID': item.itemId,
                    '商品名称': item.itemName,
                    '商品价格': item.itemPrice,
                    '佣金比例': item.tcpCommission,
                    '佣金类型': item.tcpCommissionType,
                    '热门标签': item.hotItemTag,
                    '检查时间': item.checkTime,
                    '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`
                }));

                const ws3 = XLSX.utils.json_to_sheet(hasCommissionData);
                ws3['!cols'] = [
                    {wch: 15}, {wch: 50}, {wch: 12}, {wch: 12}, {wch: 15},
                    {wch: 15}, {wch: 20}, {wch: 15}
                ];
                XLSX.utils.book_append_sheet(wb, ws3, '有佣金');
            }

            // 导出文件
            const fileName = `淘宝产品检查结果_${new Date().getTime()}.xlsx`;
            XLSX.writeFile(wb, fileName);

            layer.msg(`成功导出数据 - 不符合规则: ${notCompliant.length}个, 无佣金: ${noCommission.length}个, 有佣金: ${hasCommission.length}个`);

        } catch (error) {
            console.error('导出失败:', error);
            layer.msg('导出失败: ' + error.message);
        }
    }

    // 清空数据
    function clearData() {
        layer.confirm('确定要清空所有数据吗？', {
            btn: ['确定', '取消'],
            icon: 3,
            title: '确认清空'
        }, function(index) {
            localStorage.removeItem(STORAGE_KEY);
            updateProductCount();
            layer.msg('数据已清空');
            layer.close(index);
        });
    }

    // 显示加载状态
    function showLoading(text = '加载中...') {
        layer.load(1, {
            shade: [0.3, '#000'],
            content: text
        });
    }

    // 隐藏加载状态
    function hideLoading() {
        layer.closeAll('loading');
    }

    // 更新商品数量显示
    function updateProductCount() {
        try {
            const data = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
            document.getElementById('product-count').textContent = `已检查: ${data.length} 个商品`;
        } catch (error) {
            console.error('更新计数失败:', error);
        }
    }

    // 加载已存储的数据
    function loadStoredData() {
        updateProductCount();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initPlugin);
    } else {
        initPlugin();
    }

})();
